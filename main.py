from time import time
from fastapi import <PERSON><PERSON><PERSON>, __version__
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from apscheduler.schedulers.background import BackgroundScheduler
import requests

app = FastAPI()

from api.router import api_router  # <-- Add this line

app.include_router(api_router)  

app.mount("/static", StaticFiles(directory="static"), name="static")

html = f"""
<!DOCTYPE html>
<html>
    <head>
        <title>FastAPI on Vercel</title>
        <link rel="icon" href="/static/favicon.ico" type="image/x-icon" />
    </head>
    <body>
        <div class="bg-gray-200 p-4 rounded-lg shadow-lg">
            <h1>Hello from FastAPI@{__version__}</h1>
            <ul>
                <li><a href="/docs">/docs</a></li>
                <li><a href="/redoc">/redoc</a></li>
            </ul>
            <p>Powered by <a href="https://vercel.com" target="_blank">Vercel</a></p>
        </div>
    </body>
</html>
"""

def sync_typesense_job():
    # Adjust the URL if needed
    url = "http://localhost:8000/typesense/sync_feeddata"
    try:
        response = requests.post(url)
        print("Sync job status:", response.status_code)
    except Exception as e:
        print("Sync job failed:", e)

def start_scheduler():
    scheduler = BackgroundScheduler()
    scheduler.add_job(sync_typesense_job, 'interval', hours=5)
    scheduler.start()

@app.on_event("startup")
def startup_event():
    start_scheduler()

@app.get("/")
async def root():
    return HTMLResponse(html)

@app.get('/ping')
async def hello():
    return {'res': 'pong', 'version': __version__, "time": time()}